<dialog class="stylized_wide" #topicDialog>
  <div (click)="closeDialog()" class="x_bt"></div>
  <form [formGroup]="topicForm">
    <div class="flex flex-col cont_mod">
      <p class="pr_20 text-center">Создать тему</p>
      <div class="format-options"></div>
      <p class="auth_head">
        Название темы
      </p>
      <div class="catg_wrap">
        <input formControlName="name" type="text" placeholder="Название темы" maxlength="500">
        <span class="char-counter">{{topicForm.get('name')?.value?.length || 0}} / 500</span>
      </div>
      <p class="auth_head mt-4">
        Аватарка темы
      </p>
      <div class="custom-file-upload">
        <input type="file" accept="image/*" (change)="uploadAvatar($event)" id="avatarInput" hidden />
        <label class="relative" for="avatarInput">
          <span *ngIf="!avatarSpinner" class="icon"></span>
          <span *ngIf="!avatarSpinner">Выбрать аватарку</span>
          <div class="loading-container" *ngIf="avatarSpinner">
            <div class="spinner"></div>
          </div>
        </label>
        <div class="preview-wrapper" *ngIf="avatarPreview">
          <div class="preview-card">
            <button class="remove-btn" (click)="removeAvatar()"><span></span></button>
            <img [src]="avatarPreview.src" alt="Avatar Preview" />
            <p class="file-name">{{ avatarPreview.name }}</p>
          </div>
        </div>
      </div>
      <p class="auth_head mt-4">
        Текст
      </p>
      <textarea formControlName="content" cols="30" rows="5" placeholder="Текст темы (минимум 10 символов)"></textarea>
      <div class="custom-file-upload">
        <input type="file" multiple accept="image/*" (change)="uploadFiles($event)" id="fileInput" hidden />
        <label class="relative" for="fileInput">
          <span *ngIf="!spinner" class="icon"></span>
          <span *ngIf="!spinner">Выбрать файл</span>
          <div class="loading-container" *ngIf="spinner">
            <div class="spinner"></div>
          </div>
        </label>
        <div [ngStyle]="{maxHeight: (modalHeight < 650) ? '175px' : '368px'}" class="preview-wrapper">
          <div class="preview-card" *ngFor="let file of imagePreviews; let i = index">
            <button class="remove-btn" (click)="removeImage(i)"><span></span></button>
            <img *ngIf="file && file.type && file.type.startsWith('image/')" [src]="file.src" alt="Preview" />
            <div class="file-name" *ngIf="!file || !file.type || !file.type.startsWith('image/')">
              {{ file.extension.toUpperCase() }}
            </div>
            <p class="file-name">{{ file.name }}</p>
          </div>
        </div>
      </div>
      <div class="filter-buttons-container flex justify-between mt-4">
        <button class="save-btn" [disabled]="isLoading || topicForm.invalid" (click)="onSubmit()">
          <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Опубликовать</div>
        </button>
        <button class="save-btn" (click)="closeDialog()">
          <img class=" btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Закрыть</div>
        </button>
      </div>
    </div>
  </form>
</dialog>
<div *ngIf="category">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{category.name}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <div class="articles-search relative">
          <input class="theme_i" [(ngModel)]="searchQuery" type="text" placeholder="Поиск по темам">
          <div (click)="showDialog()" class="p_filter">
            <span class="cr_theme">Создать тему</span>
          </div>
          <div class="articles-sort_ custom-dropdown" (click)="toggleDropdownSort()">
            @if (dropdownSortOpen) {
              <div class="dropdown-content">
                @for(option of sortOptions; track option.id) {
                  <div (click)="selectSort(option.value)" class="dropdown-item cat_i" [class.active]="currentSortField === option.value">
                    {{ option.label }}
                    <span class="sort-arrow" *ngIf="currentSortField === option.value">
                      <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}" width="30"
                        height="30" viewBox="0 0 24 24">
                        <path d="M7 10l5 5 5-5H7z" />
                      </svg>
                    </span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
        <div class="ar_wrapp">
          @for(topic of filteredTopics; track topic.id) {
          <div class="article-item m">
            <div class="vis_part relative">
              <div class="art_img" (click)="router.navigate(['/ru/forum/topic/' + topic.id])">
                @if(topic.avatar) {
                <img style="object-fit: cover" width="66" height="66"
                  [src]="environment.serverUrl + '/upload/' + topic.avatar.name">
                } @else if(category.icon) {
                <img style="object-fit: cover" width="66" height="66"
                  [src]="environment.serverUrl + '/upload/' + category.icon.name">
                } @else {
                <img src="assets/images/clouds.webp" alt="image">
                }
              </div>
              <div class="flex justify-between w-full dbl_wr">
                <div class="titl_w" (click)="router.navigate(['/ru/forum/topic/' + topic.id])">
                  <div class="article-title ov_wrap">
                    {{topic.name}}
                  </div>
                  <div class="flex rticle-category">
                    <div class="article-category">Автор: {{topic.user.firstName}} {{topic.user.lastName}}</div>
                    <div class="article-category ml-6">{{getLastComment(topic.comments)}}</div>
                  </div>
                </div>
                <div class="info_bl">
                  <span>
                    <img src="assets/images/icons/cgfh.svg" alt="check">
                    {{topic.views}} просмотра(ов)
                  </span>
                  <span>
                    <img src="assets/images/icons/fframe.svg" alt="chat">
                    {{topic.comments.length}} ответа(ов)
                  </span>
                </div>
                <button type="button" *ngIf="canDeleteTopic(topic)" (click)="removeTopic($event, topic.id)" class="delete-topic-btn" title="Удалить тему">
                  <svg width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="currentColor"/>
                    <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="currentColor"/>
                    <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="currentColor"/>
                    <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          }
        </div>

        <div class="buttn_catg"  *ngIf="page < totalPages && !isLoading">
          <button class="load-more-button" (click)="nextPage()" [disabled]="isLoadingMore">
            <span>Загрузить еще</span>
          </button>
        </div>

      </div>
    </div>
  </div>
</div>
