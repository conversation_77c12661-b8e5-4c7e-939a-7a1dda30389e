import {Component, inject, PLATFORM_ID} from '@angular/core';
import {TranslocoService} from "@jsverse/transloco";
import {ActivatedRoute} from "@angular/router";
import {MypageService} from "@/services/mypage.service";
import {environment} from "@/env/environment";
import {CommonModule, isPlatformBrowser} from "@angular/common";
import {CarouselComponent} from "@/components/carousel/carousel.component";
import {Meta, Title} from "@angular/platform-browser";
import { TextInteractionComponent } from '../content/text-interaction/text-interaction.component';

@Component({
  selector: 'app-mypage',
  standalone: true,
  imports: [CommonModule, CarouselComponent, TextInteractionComponent],
  templateUrl: './mypage.component.html',
  styleUrl: './mypage.component.css'
})
export class MypageComponent {
  route = inject(ActivatedRoute);
  translocoService = inject(TranslocoService);
  mypageService = inject(MypageService);
  metaService = inject(Meta);
  titleService = inject(Title);
  platformId = inject(PLATFORM_ID);
  data: any = null
  ngOnInit() {
    this.route.params.subscribe(params => {
      const lang = this.route.parent?.snapshot.params['lang'];
      console.log(lang)
      this.mypageService.getOne(params['id'], lang).subscribe((res: any) => {
        res.carousel = res.carousel.map((item: any) => {
          return {
            size: item.size,
            title: item.title,
            items: item.items,
          }
        })
        this.data = res;
        this.titleService.setTitle(res.seo_title);
        this.metaService.updateTag({name: 'description', content: res.seo_description});
      })
    })
  }

  followLink(link: string) {
    if(isPlatformBrowser(this.platformId)){
      window.open(link)
    }
  }

  protected readonly environment = environment;
}
