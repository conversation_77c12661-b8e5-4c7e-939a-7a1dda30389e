.mypage {
  margin-top: -35px;
}

.mypage-header {
  position: relative;
  padding: 20px;
  padding-top: 90px;
}

.mypage-header img {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  object-fit: cover;
  position: absolute;
  z-index: 1;
}

/*.mypage-header > *:not(img) {*/
/*  position: relative;*/
/*  z-index: 3;*/
/*}*/

.mypage-header:before {
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  top: 0;
  left: 0;
  position: absolute;
  z-index: 2;
}

.mypage-header__text {
  z-index: 5;
  position: relative;
  color: white;
}

.mypage-title {
  font-weight: 400;
  font-size: 32px;
  text-align: center;
}

.mypage-description {
  margin-top: 40px;
}

.mypage-buttons {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.mypage-content {
  padding: 20px;
  color: white;
}

.social {
  margin: 20px 0;
}

.content_wrap_ {
  display: flex;
  cursor: pointer;
  margin-bottom: 10px;
}

.content_wrap_ .social_par {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: white;
  margin-left: 10px;
}

.content_ {
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin-right: 10px;
}

.content_._telegram {
  background: var(--cover_telegram);
  width: 28px;
  height: 24px;
}

.content_._instagram {
  background: var(--cover_instagram);
  width: 24px;
  height: 24px;
}

.content_._phone {
  background: var(--cover_phone);
  width: 27px;
  height: 19px;
}

.content_._email {
  background: var(--cover_email);
  width: 24px;
  height: 23px;
}

.mypage-image {
  margin: 40px 0;
  text-align: center;
  padding: 0 20px;
}

.mypage-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}
