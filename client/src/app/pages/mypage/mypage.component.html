<div class="mypage" *ngIf="data">
  <div class="mypage-header">
    <div class="mypage-header__text">
      <h1 class="mypage-title">{{data.title}}</h1>
      <div class="mypage-description">{{data.description}}</div>

      <div class="social" *ngIf="data.telegram || data.instagram || data.email || data.phone">
        <div class="content_wrap_" *ngIf="data.telegram">
          <div class="content_ _telegram"></div>
          <p class="social_par">{{data.telegram}}</p>
        </div>
        <div class="content_wrap_" *ngIf="data.instagram">
          <div class="content_ _instagram"></div>
          <p class="social_par">{{data.instagram}}</p>
        </div>
        <div class="content_wrap_" *ngIf="data.email">
          <div class="content_ _email"></div>
          <p class="social_par">{{data.email}}</p>
        </div>
        <div class="content_wrap_" *ngIf="data.phone">
          <div class="content_ _phone"></div>
          <p class="social_par">{{data.phone}}</p>
        </div>
      </div>

      <div class="mypage-buttons">
        <button (click)="followLink(btn.link)" class="m_btn light_ relative back_b" *ngFor="let btn of data.buttons">
          <span>{{btn.name}}</span>
        </button>
      </div>
    </div>
    <img *ngIf="data.bg" [src]="environment.serverUrl + '/upload/' + data.bg.name" alt="">
  </div>


  <div class="mypage-carousel" *ngIf="data.carousel.length">
      <div class="m_grid m-auto">
        @for(item of data.carousel; track $index) {
          <app-carousel [slidersConstructorData]="item" [interval]="2000"></app-carousel>
        }
      </div>
  </div>
  <div class="mypage-image" *ngIf="data.image">
    <img [src]="environment.serverUrl + '/upload/' + data.image.name" [alt]="data.title">
  </div>
  <!-- <div class="mypage-content" [innerHTML]="data.content"></div> -->
  <text-interaction id="content" [contentId]="data.id" [contentHtml]="data.content" class="book_text_section"></text-interaction>
 
</div>
