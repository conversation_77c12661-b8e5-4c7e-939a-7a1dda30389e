@import '../../../assets/styles/new-typography';
@import '../../../assets/styles/new-palette';

:host {
    background-color: main(50);
}

.main-banner {
    width: 100%;
    background: url(../../../assets/images/main-v2/iryna_it_cinematic_wide_angle_shot_of_a_beautiful_Hindu_temple__3cd826ee-783a-44af-a6b8-8227a4935d7b\ 1.webp) no-repeat left center;
    background-size: cover;
    height: 100vh;
    min-height: 728px;
    padding-bottom: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    h1 {
        @include h1;
        color: main(600);
        text-align: center;
        padding-top: 158px;
        margin-bottom: 40px;

        @media (max-width: 1100px) {
            padding-top: 130px;
        }

        @media (max-width: 600px) {
            @include h4;
        }
    }

    h3 {
        @include h3;
        color: main(600);
        text-align: center;
        margin-bottom: 60px;

        @media (max-width: 600px) {
            @include subtitle-4;
        }
    }

    .logo {
        margin: auto;

        @media (max-width: 1100px) {
            display: none;
        }
    }

    .call-to-action-btn {
        background-image: url(../../../assets/images/main-v2/call-to-action-button.webp);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        color: main(50);
        @include button-1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 260px;
        height: 56px;
        // box-shadow: 0px 3px 20.2px 0px #160E02C9;

        .btn-label {
            border-radius: 100px;
            // backdrop-filter: blur(15.399999618530273px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            width: 100%;
            height: 100%;
            // margin-bottom: 3px;
        }
    }
}

.main-contetnt-wrapper {
    max-width: 1440px;
    width: 100%;
    margin: 0 auto;
    padding: 110px 0;
    overflow-x: hidden;

    .carousel-header {
        max-width: 1200px;
        margin: 0 auto 100px auto;
        color: main(600);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
        @media (max-width: 1100px) {
            margin: 0 auto 40px auto;
        }
        @media (max-width: 650px) {
            margin: 0 auto 33px auto;
            justify-content: flex-start;
        }

        .carousel-title {
            @include h2;
            line-height: 100%;
            svg {
                display: none;
            }

            @media (max-width: 1100px) {
                @include h4;
            }

            @media (max-width: 650px) {
                display: flex;
                align-items: center;
                gap: 16px;
                width: 100%;
                justify-content: flex-start;
                @include button-2;
                svg {
                    display: block;
                }

            }
        }
        
        a {
            @include button-1;
            color: main(700);
            transition: all 0.2s ease-in-out;
            @media (max-width: 1100px) {
                @include button-4;
            }
    
            @media (max-width: 650px) {
                display: none;
            }

            &:hover {
                color: main(500);
            }

            &:active {
                color: main(600);
            }
        }
    }
}

.join-cards-wrapper {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    padding: 0 15px;
    @media (max-width: 1100px) {
        max-width: 800px;
        margin: 0 auto;
    }
    @media (max-width: 650px) {
        gap: 32px;
        flex-direction: column;
    }
    app-join-card {
        flex: 1 1 16%;
        max-width: 210px;
        transition: all 0.2s ease-in-out;
        @media (max-width: 1100px) {
            max-width: 100px;
        }
        @media (max-width: 650px) {
            max-width: 100%;
            flex: none;
        }
        
        &:hover {
            max-width: 280px;
            flex: 1 1 20%;
            
            @media (max-width: 1100px) {
                max-width: 155px;
            }

            @media (max-width: 650px) {
                max-width: 100%;
                flex: none;
            }
        }
    }
}

section {
    margin-bottom: 260px;
    @media (max-width: 1100px) {
        margin-bottom: 160px;
    }
    @media (max-width: 650px) {
        margin-bottom: 100px;
    }
}



.projects-and-ministry-section {
    .projects-tabs {
        padding: 0 15px;
        width: 100%;
        margin-bottom: 44px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        scroll-behavior: smooth;
        position: relative;
        &::-webkit-scrollbar {
            display: none;
        }
        
        &:after {
            width: 100%;
            content: "";
            height: 2px;
            background: main(200);
            position: sticky;
            bottom: 1px;
            left: 15px;
            display: block;
        }
        @media (max-width: 1100px) {
            margin-bottom: 22px;
            padding: 0 35px;
        }
        @media (max-width: 650px) {
            padding: 0 16px;
        }
        .projects-tabs-wrapper {
            display: flex;
            justify-content: space-between;
            gap: 24px;
        }

        .tab-item {
            height: 96px;
            padding: 32px 0;
            color: main(600);
            @include subtitle-1;
            cursor: pointer;
            opacity: 0.5;
            transition: all 0.2s ease-in-out;
            white-space: nowrap;
            border-bottom: 1px solid main(200);
            @media (max-width: 1100px) {
                height: 60px;
                @include body-2;
                padding: 20px 0;
            }
            @media (max-width: 1100px) {
                height: 50px;
                @include button-5;
                padding: 16px 0;
            }

            &.active {
                opacity: 1;
                border-bottom: 2px solid main(400);
                position: sticky;
                z-index: 2;
            }
        }
    }

    .selected-tab-content {
        display: flex;
        gap: 24px;
        justify-content: space-between;
        max-width: 81%;
        margin: 0 auto;
        @media (max-width: 1100px) {
            max-width: 100%;
            padding: 0 35px;
        }
        @media (max-width: 650px) {
            max-width: 400px;
            padding: 0 16px;
            position: relative;
        }
        
        .tab-text-content {
            padding: 45px 0;
            @media (max-width: 1100px) {
                padding: 35px 0;
            }
            @media (max-width: 650px) {
                padding: 0 0 42px;
            }
            
            .tab-date {
                @include caption-2;
                color: main(500);
                margin-bottom: 30px;
                @media (max-width: 1100px) {
                    margin-bottom: 20px;
                }
                @media (max-width: 650px) {
                    @include caption-3;
                }
            }
            
            .tab-description {
                @include body-1;
                color: main(600);
                margin-bottom: 50px;
                max-width: 527px;
                @media (max-width: 1100px) {
                    margin-bottom: 26px;
                    max-width: 340px;
                    @include body-3;
                }
                @media (max-width: 650px) {
                    max-width: 256px;
                }
            }

            .primaty-button {
                background: url(../../../assets/images/main-v2/stroke-button-md.webp) no-repeat center;
                cursor: pointer;
                width: 175px;
                height: 42px;
                display: flex;
                align-items: center;
                justify-content: center;
                @include button-2;
                color: main(600);
                background-size: cover;
                transition: all 0.2s ease-in-out;
                &:hover {
                    background: url(../../../assets/images/main-v2/stroke-button-md-hover.webp) no-repeat center;
                }
                &:active {
                    background: url(../../../assets/images/main-v2/stroke-button-md-active.webp) no-repeat center;
                }
                @media (max-width: 650px) {
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translate(-50%, 0);
                }
            }
        }

        .tab-image {
            width: 453px;
            height: fit-content;
            // padding: 1px;
            position: relative;
            @media (max-width: 1100px) {
                width: 340px;
            }
            @media (max-width: 650px) {
                width: 78px;
            }

            .mask {
                width: 453px;
                height: 461px;
                background: url(../../../assets/images/main-v2/tab-mask.webp) no-repeat center;
                position: sticky;
                z-index: 2;
                @media (max-width: 1100px) {
                    width: 340px;
                    height: 348px;
                    background-size: contain;
                }
                @media (max-width: 650px) {
                    width: 78px;
                    height: 80px;
                }
            }

            img {
                width: 98%;
                height: 97%;
                object-fit: cover;
                border-radius: 8px;
                position: absolute;
                top: 1px;
                left: 1px;
                transform: translate(1%, 1%);
            }
        }
    }
}