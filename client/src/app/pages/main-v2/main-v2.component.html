

<div class="main-banner">
  <div>
    <h1>
      Всемирная Община Санатана Дхармы
    </h1>
    <h3>
      Да распространится Дхарма во всех мирах
    </h3>
    <img class="logo" src="../../../assets/images/main-v2/om_big 2.webp" alt="logo">
  </div>
  <div class="call-to-action-btn">
    <div class="btn-label">
      Начать свой путь
    </div>
  </div>
</div>

<div class="main-contetnt-wrapper">
  <section class="chronological-section">
    <div class="carousel-header">
      <div class="carousel-title">
        Мероприятия и он-лайн ритриты
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <app-carousel-v2 [items]="events" [itemsPerView]="itemsPerView">
      <ng-template let-event>
        <app-chronological-card [value]="event"></app-chronological-card>
      </ng-template>
    </app-carousel-v2>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <section class="start-learning-section">
    <div class="carousel-header">
      <div class="carousel-title">
        Начните обучение
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <app-scaled-carousel [items]="courses" [itemsPerView]="4" (activeSlideChange)="selectedCourseChange($event)" >
      <ng-template let-course>
        <app-accent-card [value]="course" [isActive]="course.name === selectedCourse().name"></app-accent-card>
      </ng-template>
    </app-scaled-carousel>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="itemsPerView">
      <ng-template let-course>
        <app-accent-card [value]="course"></app-accent-card>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <section class="chronological-section">
    <div class="carousel-header">
      <div class="carousel-title mx-auto">
        Библиотека мудости
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    <app-carousel-v2 [items]="events" [itemsPerView]="itemsPerView">
      <ng-template let-event>
        <app-card-with-domes [value]="event"></app-card-with-domes>
      </ng-template>
    </app-carousel-v2>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <!-- <app-chronological-card></app-chronological-card> -->
  <!-- <app-accent-card></app-accent-card>
  <app-card-with-dome></app-card-with-dome> -->

  <section class="projects-and-ministry-section">
    <div class="carousel-header flex-col gap-[34px]">
      <div class="carousel-title">
        Проекты и служение
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <div class="projects-tabs" #projectsTabs>
      <div class="projects-tabs-wrapper">
        @for (tab of projectTabs; track tab) {
          <div 
            class="tab-item cursor-pointer"
            [class.active]="tab.title === activeProjectTab.title"
            (click)="setActiveProjectTab(tab)"
          >
            {{tab.title}}
          </div>
        }
      </div>
    </div>
    <div class="selected-tab-content">
      <div class="tab-text-content">
        <div class="tab-date">{{activeProjectTab.date}}</div>
        <div class="tab-description">{{activeProjectTab.description}}</div>
        <div class="primaty-button">Подробнее</div>
      </div>
      <div class="tab-image">
        <div class="mask"></div>
        <img [src]="activeProjectTab.imageUrl" alt="">
      </div>
    </div>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <section class="join-section">
    <div class="carousel-header">
      <div class="carousel-title mx-auto">
        Присоедииться
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>

      </div>
    </div>
    <div class="join-cards-wrapper">
      @for (card of joinCards; track card.name) {
        <app-join-card [value]="card"></app-join-card>
      }
    </div>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
</div>
