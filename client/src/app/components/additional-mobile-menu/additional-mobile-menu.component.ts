import { Component, EventEmitter, inject, Inject, Output, PLATFORM_ID, signal } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { OnInit, OnDestroy } from '@angular/core';
import { ShareDataService } from '@/services/share-data.service';
import { Router, RouterLink } from '@angular/router';

@Component({
  selector: 'app-additional-mobile-menu',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './additional-mobile-menu.component.html',
  styleUrls: ['./additional-mobile-menu.component.scss']
})
export class AdditionalMobileMenuComponent implements OnInit, OnDestroy {
  @Output() sideOpen = new EventEmitter<any>();
  currentLanguage = signal<string>('ru');
  router = inject(Router);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private shareDataService: ShareDataService
  ) { }

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {

    }
    this.shareDataService.playCard$.subscribe(() => {

    })
  }

  ngOnDestroy() {

  }

  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
  }

  toggleSidebar() {
    console.log(this.sideOpen);

    this.sideOpen.emit(true);
  }
}
