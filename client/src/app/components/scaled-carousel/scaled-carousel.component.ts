import {
  Component,
  Input,
  ContentChild,
  TemplateRef,
  signal,
  computed,
  OnInit,
  effect,
  EventEmitter,
  Output
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-scaled-carousel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './scaled-carousel.component.html',
  styleUrl: './scaled-carousel.component.scss'
})
export class ScaledCarouselComponent<T> implements OnInit {
  @Input({ required: true }) items: T[] = [];
  @Input() itemsPerView = 5;
  @Input() itemWidth = 330;
  @Output() activeSlideChange = new EventEmitter<T>();

  activeSlide = computed(() => {
    const currentRealIndex = this.getCurrentRealIndex();
    return this.items[currentRealIndex] || null;
  });

  @ContentChild(TemplateRef) itemTemplate!: TemplateRef<any>;

  currentIndex = signal(0);

    constructor() {
    // Эффект для отправки изменений activeSlide наружу
    effect(() => {
      const active = this.activeSlide();
      if (active) {
        this.activeSlideChange.emit(active);
      }
    });
  }
  
  // Простой расширенный массив - просто дублируем исходный массив 3 раза
  extendedItems = computed(() => {
    if (this.items.length === 0) return [];
    return [...this.items, ...this.items, ...this.items];
  });

  ngOnInit() {
    // Начинаем со второй копии массива (средней)
    this.currentIndex.set(this.items.length);
  }

  // Получаем смещение для всей дорожки
  getTrackTransform(): string {
    const gap = 20;
    const totalItemWidth = this.itemWidth + gap;
    const centerOffset = Math.floor(this.itemsPerView / 2);
    const translateX = -(this.currentIndex() - centerOffset) * totalItemWidth;
    return `translateX(${translateX}px)`;
  }

  // Получаем индекс элемента относительно центра
  getRelativeIndex(itemIndex: number): number {
    return itemIndex - this.currentIndex();
  }

  // Определяем масштаб для элемента
  getScale(itemIndex: number): number {
    const relativeIndex = Math.abs(this.getRelativeIndex(itemIndex));
    
    if (relativeIndex === 0) return 1.1; // Центральный элемент
    if (relativeIndex === 1) return 1.0; // Соседние элементы
    return 0.9; // Остальные элементы
  }

  // Проверяем, виден ли элемент
  isVisible(itemIndex: number): boolean {
    const relativeIndex = Math.abs(this.getRelativeIndex(itemIndex));
    const halfView = Math.floor(this.itemsPerView / 2);
    return relativeIndex <= halfView;
  }

  // Определяем, должен ли элемент быть обрезан
  isClipped(itemIndex: number): boolean {
    if (this.itemsPerView % 2 !== 0) return false; // Только 
    // для четных значений
    
    const relativeIndex = this.getRelativeIndex(itemIndex);
    const halfView = Math.floor(this.itemsPerView / 2);
    
    return Math.abs(relativeIndex) === halfView;
  }

  next() {
    const newIndex = this.currentIndex() + 1;
    // Если дошли до конца второй копии, перескакиваем на начало второй копии
    if (newIndex >= this.items.length * 2) {
      this.currentIndex.set(this.items.length);
    } else {
      this.currentIndex.set(newIndex);
    }
  }

  prev() {
    const newIndex = this.currentIndex() - 1;
    // Если дошли до начала второй копии, перескакиваем на конец второй копии
    if (newIndex < this.items.length) {
      this.currentIndex.set(this.items.length * 2 - 1);
    } else {
      this.currentIndex.set(newIndex);
    }
  }

  goTo(targetIndex: number) {
    // Переходим к элементу во второй копии массива
    this.currentIndex.set(this.items.length + targetIndex);
  }

  // Получаем реальный индекс в исходном массиве
  getRealIndex(extendedIndex: number): number {
    return extendedIndex % this.items.length;
  }

  // Получаем текущий реальный индекс
  getCurrentRealIndex(): number {
    return this.getRealIndex(this.currentIndex());
  }
}
