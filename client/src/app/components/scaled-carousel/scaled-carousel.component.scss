@import '../../../assets/styles/new-palette';

.carousel {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 40px 0;

  .carousel-track-wrapper {
    overflow: hidden;
    display: flex;
    align-items: center;
    min-height: 540px;
    position: relative;
    margin-left: -145px;
  }

  .carousel-track {
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform; // Оптимизация для анимаций
  }

  .carousel-item {
    flex: 0 0 auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transform-origin: center;
    
    &.visible {
      opacity: 1;
    }
    
    &.center {
      z-index: 10;
      
      // &::after {
      //   content: '';
      //   position: absolute;
      //   top: -10px;
      //   left: -10px;
      //   right: -10px;
      //   bottom: -10px;
      //   border: 2px solid main(300);
      //   border-radius: 12px;
      //   opacity: 0.6;
      //   pointer-events: none;
      // }
    }

    &.clipped {
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0.3) 0%,
          transparent 20%,
          transparent 80%,
          rgba(255, 255, 255, 0.3) 100%
        );
        pointer-events: none;
        z-index: 1;
      }
    }

    &:not(.visible) {
      opacity: 0;
      pointer-events: none;
    }
  }

  .nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 246, 224, 0.5019607843);
    border: 1px solid main(300);
    border-radius: 50%;
    cursor: pointer;
    z-index: 20;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      // background: main(100);
      border-color: main(400);
    }

    &.left {
      left: 20px;
    }
    
    &.right {
      right: 20px;
    }

    img {
      width: 24px;
      height: 24px;
    }
  }

  .indicators {
    text-align: center;
    margin-top: 40px;
    display: flex;
    justify-content: center;
    gap: 12px;

    .dot {
      width: 32px;
      height: 4px;
      background: main(200);
      border-radius: 2px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: main(400);
      }

      &.active {
        background: main(600);
        transform: scaleX(1.5);
      }
    }
  }
}

@media (max-width: 768px) {
  .carousel {
    .carousel-track {
      gap: 15px;
    }
    
    .nav {
      width: 50px;
      height: 50px;
      
      &.left {
        left: 10px;
      }
      
      &.right {
        right: 10px;
      }
      
      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}