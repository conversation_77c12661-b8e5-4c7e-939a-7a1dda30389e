@if (showDailySidebar() || showMobileSidebar()) {
  <div class="sidebar-mask" (click)="closeSidebar()"></div>
}
<div class="daily-sidebar-container" [class.daily-sidebar-is-open]="showDailySidebar() || showMobileSidebar()">
  <div class="marker-section">
    <div
      (click)="toogleSidebar()"
      class="marker cursor-pointer"
      [class.dark-wheel]="isDarkMarker() && !showDailySidebar()"
    >
    </div>
  </div>
  <div class="main-sidebar-section">
    <div class="side-bar-header">
      4 сентября 2025
      <svg class="cursor-pointer" (click)="closeSidebar()" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 6L17.3137 17.3137" stroke="#351F04" stroke-width="2" stroke-linecap="round"
          stroke-linejoin="round" />
        <path d="M6 17L17.3137 5.68629" stroke="#351F04" stroke-width="2" stroke-linecap="round"
          stroke-linejoin="round" />
      </svg>
    </div>
    <div class="sidebar-body">
      <div class="daily-name">Экадаши - день духовной практики</div>
      <div class="daily-description">
        Благоприятный день для медитации, чтения священных текстов и духовных практик. Рекомендуется легкое питание или
        пост.
      </div>
      <div class="daily-mantra-box">
        <div class="mantra-title">Мантра дня</div>
        <div class="mantra-name">
          ॐ नमः शिवाय<br>
          Ом Намах Шивая
        </div>
      </div>
      <div class="upcoming-events-box">
        <div class="upcoming-events-box_title">Ближайшие мероприятия</div>
        <div class="schedule-section">
          <div class="schedule-dash-marker"></div>
          @for (item of viewSchedule; track $index) {
            <div class="schedule-point-box">
              <div 
                class="day-box" 
                [class.items-center]="item.events?.length === 1"
                >
                <div class="date" 
                  [class.today]="$index === 0"
                  [class.default-m]="item.events?.length >= 2"
                >
                  @if ($index === 0) {
                    Сегодня
                  } @else {
                    <div class="day">{{item.day}}</div>
                    <div class="month">{{item.month}}</div>
                  }
                </div>
              </div>
              <div class="schedule-point-info-cards">
                @for (event of item.events; track event.name) {
                  <div class="info-card">
                    <div class="img-section" [style.background-image]="'url(' + event.imageUrl + ')'">
                      <div class="img-section_mask"></div>
                    </div>
                    <div class="info-box">
                      <div class="title">{{event.name}}</div>
                      <div class="tag">{{event.tag}}</div>
                    </div>
                  </div>
                }
              </div>
            </div>
          }
          @if (viewSchedule.length && viewSchedule.length !== schedule.length) {
            <div class="load-more" (click)="loadMore()">Смотреть больше</div>
          }
        </div>

      </div>
    </div>