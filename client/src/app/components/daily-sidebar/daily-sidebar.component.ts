import {
  Component,
  signal,
  input,
  output
} from '@angular/core';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-daily-sidebar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './daily-sidebar.component.html',
  styleUrl: './daily-sidebar.component.scss'
})
export class DailySidebarComponent {
  readonly showDailySidebar = signal(false);
  readonly isDarkMarker = input<boolean>(false);
  readonly showMobileSidebar = input<boolean>(false);
  readonly closeDailySidebar = output<void>();

  schedule: any = [
    {
      day: '4', month: 'сентября', events: [
        {
          name: 'День Карма-йоги: Служение общине',
          description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
          date: '15-20 мая 2025',
          imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
          tag: "Мероприятие",
        },
        {
          name: 'Празднование Будда Пурнимы',
          description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
          date: '15-20 мая 2025',
          tag: "Мероприятие",
          imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        },
      ]
    },
    {
      day: '5', month: 'сентября', events: [
        {
          name: 'Празднование Будда Пурнимы',
          description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
          date: '15-20 мая 2025',
          imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
          tag: "Мероприятие",
        },
      ]
    },
    {
      day: '6', month: 'сентября', events: [{
        name: 'Лекция о Веданте',
        description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
        date: '15-20 мая 2025',
        imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        tag: "Мероприятие",
      },
      ]
    },
    {
      day: '7', month: 'сентября', events: [{
        name: 'Ретрит по медитации',
        description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
        date: '15-20 мая 2025',
        imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        tag: "Мероприятие",
      },
      ]
    },
    {
      day: '8', month: 'сентября', events: [
        {
          name: 'День Карма-йоги: Служение общине',
          description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
          date: '15-20 мая 2025',
          imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
          tag: "Мероприятие",
        },
      ]
    },
  ];
  viewSchedule: any = [this.schedule[0], this.schedule[1], this.schedule[2]];

  loadMore() {
    const currentLength = this.viewSchedule.length;
    const nextItems = this.schedule.slice(currentLength, currentLength + 2);
    this.viewSchedule = this.viewSchedule.concat(nextItems);
  }

  openSidebar() {
    this.showDailySidebar.set(true);
  }

  toogleSidebar() {
    this.showDailySidebar.set(!this.showDailySidebar());
  }

  closeSidebar() {
    this.showDailySidebar.set(false);
    this.closeDailySidebar.emit();
  }
}
