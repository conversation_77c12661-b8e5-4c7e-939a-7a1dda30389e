@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.chronological-card {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 1px;
    background-color: main(50);
    &:hover {
        .hover-mask {
            opacity: 1;
            z-index: 2;
        }
        .secondary-btn {
            opacity: 1;
        }
    }
    .background-mask {
        z-index: 3;
    }

    .card-img {
        border-radius: 6px;
        max-height: 228px;
        object-fit: cover;
    }

    .card-date {
        color: main(600);
        margin: 8px 0;
        @include caption-1;
        line-height: 100%;
        letter-spacing: 0;
        vertical-align: middle;
    }

    .card-title {
        @include subtitle-1;
        color: main(700);
    }

    .card-description {
        color: main(500);
        @include body-1;
    }

    .hover-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        border-radius: 6px;
        top: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.021);
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        max-height: 229px;
    }
    .secondary-btn {
        top: 50%;
        left: 50%;
        position: absolute;
        transform: translate(-50%, -30%);
        opacity: 0;
        background: url(../../../../assets/images/main-v2/secondary-button_l.webp) no-repeat center;
        background-size: contain;
        cursor: pointer;
        width: 170px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        @include button-2;
        color: main(600);
        transition: all 0.2s ease-in-out;
        &:hover {
            background: url(../../../../assets/images/main-v2/secondary-button_l-hover.webp) no-repeat center;
            height: 42px;
        }
    }
}