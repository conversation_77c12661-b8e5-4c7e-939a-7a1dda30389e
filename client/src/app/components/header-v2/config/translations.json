[{"id": 15, "code": "content.news.published", "translations": [{"lang": "ru", "text": "Опубликована новость"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 20, "code": "header.nav.main", "translations": [{"lang": "ru", "text": "Главная"}, {"lang": "en", "text": "Home"}, {"lang": "de", "text": "Startseite"}, {"lang": "ua", "text": "Головна"}, {"lang": "it", "text": "Home"}]}, {"id": 21, "code": "header.nav.forum", "translations": [{"lang": "ru", "text": "Форум"}, {"lang": "en", "text": "Forum"}, {"lang": "de", "text": "Forum"}, {"lang": "ua", "text": "Форум"}, {"lang": "it", "text": "Forum"}]}, {"id": 22, "code": "header.nav.support", "translations": [{"lang": "ru", "text": "Поддержать"}, {"lang": "en", "text": "Support"}, {"lang": "de", "text": "Unterstützen"}, {"lang": "ua", "text": "Підтримати"}, {"lang": "it", "text": "Supporta"}]}, {"id": 23, "code": "header.menu.title", "translations": [{"lang": "ru", "text": "<PERSON>е<PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON>е<PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 24, "code": "header.menu.show_more", "translations": [{"lang": "ru", "text": "Смотреть больше"}, {"lang": "en", "text": "Show more"}, {"lang": "de", "text": "<PERSON><PERSON> anzeigen"}, {"lang": "ua", "text": "Показати більше"}, {"lang": "it", "text": "Mostra di più"}]}, {"id": 25, "code": "header.menu.close", "translations": [{"lang": "ru", "text": "Закрыть меню"}, {"lang": "en", "text": "Close menu"}, {"lang": "de", "text": "<PERSON><PERSON> sch<PERSON>ßen"}, {"lang": "ua", "text": "Закрити меню"}, {"lang": "it", "text": "<PERSON>udi menu"}]}, {"id": 26, "code": "header.menu.open", "translations": [{"lang": "ru", "text": "Открыть меню"}, {"lang": "en", "text": "Open menu"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Відкрити меню"}, {"lang": "it", "text": "Apri menu"}]}, {"id": 27, "code": "header.dropdown.tradition.sanatana_dharma", "translations": [{"lang": "ru", "text": "Санатана Драхма"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "ua", "text": "Сана<PERSON><PERSON><PERSON> Дхарма"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON> Dharma"}]}, {"id": 28, "code": "header.dropdown.tradition.lineage", "translations": [{"lang": "ru", "text": "Линия передачи"}, {"lang": "en", "text": "Lineage"}, {"lang": "de", "text": "Übertragungslinie"}, {"lang": "ua", "text": "Лінія передачі"}, {"lang": "it", "text": "Lignaggio"}]}, {"id": 29, "code": "header.dropdown.tradition.guru", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "<PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 30, "code": "header.dropdown.tradition.creed", "translations": [{"lang": "ru", "text": "Символ веры"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 31, "code": "header.dropdown.education.courses", "translations": [{"lang": "ru", "text": "Курсы"}, {"lang": "en", "text": "Courses"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Кур<PERSON>и"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 32, "code": "header.dropdown.education.basic_course", "translations": [{"lang": "ru", "text": "Базовый курс"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 33, "code": "header.dropdown.education.institute", "translations": [{"lang": "ru", "text": "Институт Васиштхи"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 34, "code": "header.dropdown.education.system", "translations": [{"lang": "ru", "text": "Система обучения"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 35, "code": "header.dropdown.practice.yoga_types", "translations": [{"lang": "ru", "text": "Джняна-йога, Бхакти-йога и т.д"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 36, "code": "header.dropdown.practice.retreats", "translations": [{"lang": "ru", "text": "Ретриты"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 37, "code": "header.dropdown.practice.mantra_institute", "translations": [{"lang": "ru", "text": "Институт Мантры и ритуалы Васиштхи"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 38, "code": "header.dropdown.practice.become_student", "translations": [{"lang": "ru", "text": "Как стать учеником?"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 39, "code": "header.dropdown.library.photo_gallery", "translations": [{"lang": "ru", "text": "Фотогалерея"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 40, "code": "header.dropdown.library.radio", "translations": [{"lang": "ru", "text": "Радио"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 41, "code": "header.dropdown.events.calendar", "translations": [{"lang": "ru", "text": "Календарь событий"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 42, "code": "header.dropdown.events.seminars", "translations": [{"lang": "ru", "text": "Семинары"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 43, "code": "header.burger.tradition.title", "translations": [{"lang": "ru", "text": "O Традиции"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 44, "code": "header.burger.start_path.title", "translations": [{"lang": "ru", "text": "Начать путь"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 45, "code": "header.burger.philosophy.title", "translations": [{"lang": "ru", "text": "Философия"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 46, "code": "header.burger.education.title", "translations": [{"lang": "ru", "text": "Обучение"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 47, "code": "header.burger.community.title", "translations": [{"lang": "ru", "text": "Община"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 48, "code": "header.burger.events.title", "translations": [{"lang": "ru", "text": "События"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 49, "code": "header.burger.media.title", "translations": [{"lang": "ru", "text": "Медиа"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 50, "code": "header.burger.practice.title", "translations": [{"lang": "ru", "text": "Практика"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 51, "code": "header.burger.support.title", "translations": [{"lang": "ru", "text": "Поддержать"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 52, "code": "header.burger.forum.title", "translations": [{"lang": "ru", "text": "Форум"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 53, "code": "header.burger.tradition.foundation.title", "translations": [{"lang": "ru", "text": "Основа"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 54, "code": "header.burger.tradition.foundation.sanatana_dharma", "translations": [{"lang": "ru", "text": "Сана<PERSON><PERSON><PERSON> Дхарма"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 55, "code": "header.burger.tradition.foundation.creed", "translations": [{"lang": "ru", "text": "Символ веры"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 56, "code": "header.burger.tradition.foundation.lineage", "translations": [{"lang": "ru", "text": "Линия передачи"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 57, "code": "header.burger.tradition.foundation.paramguru", "translations": [{"lang": "ru", "text": "Пара<PERSON>гуру"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 58, "code": "header.burger.tradition.foundation.juna_akhara", "translations": [{"lang": "ru", "text": "<PERSON>р<PERSON><PERSON><PERSON> Джуна Акхара"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 59, "code": "header.burger.tradition.guru_divine.title", "translations": [{"lang": "ru", "text": "Гуру и Божественное"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 60, "code": "header.burger.tradition.vows_path.title", "translations": [{"lang": "ru", "text": "Обеты и путь"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 16, "code": "forum.topic.created_activity", "translations": [{"lang": "ru", "text": "В вашей теме появилось новое сообщение"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 17, "code": "forum.post.reply", "translations": [{"lang": "ru", "text": "Кто-то ответил на ваше сообщение"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 18, "code": "forum.post.like", "translations": [{"lang": "ru", "text": "Кто-то поставил лайк вашему сообщению"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}, {"id": 19, "code": "forum.post.favorited", "translations": [{"lang": "ru", "text": "Кто-то добавил ваше сообщение в избранное"}, {"lang": "en", "text": ""}, {"lang": "de", "text": ""}, {"lang": "ua", "text": ""}, {"lang": "it", "text": ""}]}]