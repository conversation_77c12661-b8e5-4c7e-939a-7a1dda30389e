import { HttpClient, HttpParams } from "@angular/common/http";
import { inject, Injectable } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';

@Injectable({
  providedIn: 'root'
})
export class AdvertisingService {
  http = inject(HttpClient);
  translocoService = inject(TranslocoService);

  getAll() {
    const lang = this.translocoService.getActiveLang();
    let params = new HttpParams().set('lang', lang);
    return this.http.get('/client/advertising', { params })
  }
}
