import { File } from "@/entity/File"
import { ForumCategory } from "@/entity/ForumCategory"
import { ForumTopicComment } from "@/entity/ForumTopicComment"
import { ForumTopicFavorite } from "@/entity/ForumTopicFavorite"
import { ForumTopicLike } from "@/entity/ForumTopicLike"
import { User } from "@/entity/User"
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

@Entity()
export class ForumTopic extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number

  @CreateDateColumn({ name: 'created_at' }) createdAt: Date;
  @UpdateDateColumn({ name: 'updated_at' }) updatedAt: Date;

  @Column()
  name: string

  @Column({nullable: true})
  description: string

  @Column()
  content: string

  @Column({default: 0})
  views: number

  @ManyToOne(() => ForumCategory, category => category.topics, {onDelete: 'CASCADE'})
  @JoinColumn()
  category: ForumCategory;

  @OneToMany(() => ForumTopicComment, comment => comment.topic)
  comments: ForumTopicComment[];

  @ManyToOne(() => User, user => user.forumTopics, { onDelete: 'CASCADE' })
  user: User;

  @ManyToMany(() => File)
  @JoinTable()
  images: File[]

  @ManyToOne(() => File, { nullable: true })
  @JoinColumn()
  avatar: File

  @OneToMany(() => ForumTopicLike, comment => comment.topic, { onDelete: 'CASCADE' })
  likes: ForumTopicLike[];

  @OneToMany(() => ForumTopicFavorite, comment => comment.topic)
  favorites: ForumTopicFavorite[];

  @Column({ default: () => "CURRENT_TIMESTAMP", type: 'timestamp' })
  lastReplyAt: Date;

  @Column({default: false})
  disableComments: boolean;
}