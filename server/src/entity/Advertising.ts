import { File } from "@/entity/File"
import { BaseEntity, Column, Entity, JoinColumn, OneToOne, PrimaryGeneratedColumn } from "typeorm"

@Entity()
export class Advertising extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({nullable: true})
    active: boolean;

    @Column({nullable: true})
    advertising: boolean;

    @Column()
    title: string

    @Column()
    description: string

    @Column()
    link: string

    @Column({nullable: true})
    type: string

    @Column({nullable: true})
    freq: number

    @Column('date', { nullable: true })
    date: Date

    @Column({ type: 'varchar', nullable: true })
    slug: string;

    @Column({ type: 'varchar' })
    lang: string;

    @OneToOne(() => File, file => file.advertising, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    image: File | null
}