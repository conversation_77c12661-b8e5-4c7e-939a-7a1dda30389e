import { BaseEntity, Column, En<PERSON><PERSON>, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from "typeorm"
import { File } from "./File"
import { User } from "./User"

@Entity()
export class Mypage extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({nullable: true})
    parent: number;

    @Column()
    lang: string;

    @Column()
    title: string;

    @Column({nullable: true})
    description: string;

    @Column({nullable: true})
    seo_title: string;

    @Column({nullable: true})
    seo_description: string;

    @Column({nullable: true})
    content: string;

    @Column({type: 'json', nullable: true})
    buttons: {index: number, name: string, link: string}[];

    @Column({type: 'json', nullable: true})
    carousel: {index: number, size: string, type: string, link: string, date: string, text: string, image: string, show: boolean, items: {image: any}[]}[];

    @Column({nullable: true})
    bg: string;

    @Column({nullable: true})
    telegram: string;

    @Column({nullable: true})
    instagram: string;

    @Column({nullable: true})
    email: string;

    @Column({nullable: true})
    phone: string;

    @OneToOne(() => File, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    image: File | null;

    @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    author: User | null;
}