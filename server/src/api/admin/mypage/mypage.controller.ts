import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Delete, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common'
import { MypageService } from './mypage.service'

@Controller('admin/mypage')
@Groups('PERSONALPAGE_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class MypageController {
  constructor(private readonly mypageService: MypageService) {}

  @Get()
  async getAll(
    @Query('authorId') authorId?: number,
    @Query('onlyMy') onlyMy?: any,
    @Req() req?: any
  ) {
    const filters: any = {
      authorId: authorId ? Number(authorId) : undefined,
      onlyMy: onlyMy === 'true',
      userId: req?.user?.id
    };
    return await this.mypageService.getAll(filters)
  }

  @Get('users')
  async getUsers() {
    return await this.mypageService.getUsers()
  }

  @Get(':id')
  async getOne(@Param('id') id: number) {
    return await this.mypageService.getOne(id)
  }

  @Post()
  async create(@Body() body: any, @Req() req: any) {
    return await this.mypageService.create(body, req.user?.id)
  }

  @Delete(':id')
  async removePage(@Param('id') id: number) {
    return await this.mypageService.removePage(id)
  }
}
