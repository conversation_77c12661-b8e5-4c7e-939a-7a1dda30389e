import { FileService } from "@/api/file/file.service"
import { Mypage } from "@/entity/Mypage"
import { User } from "@/entity/User"
import { Body, Injectable } from '@nestjs/common'
import * as cheerio from "cheerio"
import { copyFileSync, existsSync, rmSync } from "fs"
import { basename } from 'path'

@Injectable()
export class MypageService {
    constructor(private readonly fileService: FileService) {}

    async getAll(filters?: { authorId?: number, onlyMy?: boolean, userId?: number }) {
        const queryBuilder = Mypage.createQueryBuilder('mypage')
            .leftJoinAndSelect('mypage.author', 'author')
            .where('mypage.parent IS NULL');

            console.log(filters);

        if (filters?.onlyMy && filters?.userId) {
            queryBuilder.andWhere('mypage.author = :userId', { userId: filters.userId });
        } else if (filters?.authorId) {
            queryBuilder.andWhere('mypage.author = :authorId', { authorId: filters.authorId });
        }

        return await queryBuilder.getMany();
    }

    async getOne(id: number) {
        return await Mypage.find({
            where: [
                {parent: id},
                {id}
            ],
            relations: ['image']
        })
    }

    async create(@Body() body: any, userId?: number) {
        const parentItem = body.find(e => e.lang === 'ru')
        const parent = await this.createPage({...parentItem, authorId: userId});

        for(let additional of body.filter(e => e.lang !== 'ru')) {
            await this.createPage({...additional, parent: parent.id, authorId: userId});
        }
    }

    async createPage(fields: any) {
        const savedFiles = new Set<string>();

        // Сохраняем фон (если есть)
        if (fields.bg?.name && !fields.bg.id) {
            const oldBg = fields.id ? JSON.parse((await Mypage.findOneBy({ id: fields.id }))?.bg || '{}') : null;
            fields.bg = await this.fileService.save(fields.bg.originalName, 'personalPage', fields.bg.name, oldBg, fields.bg.description);
            savedFiles.add(fields.bg.name);
        }

        if(fields.bg?.id) {
            await this.fileService.update(fields.bg.id, {description: fields.bg.description})
        }

        if (fields.image?.name && !fields.image.id) {
            const oldImage = fields.id ? (await Mypage.findOne({ where: { id: fields.id }, relations: ['image'] }))?.image : null;
            fields.image = await this.fileService.save(fields.image.originalName, 'personalPage', fields.image.name, oldImage, fields.image.description);
            savedFiles.add(fields.image.name);
        }

        if(fields.image?.id) {
            await this.fileService.update(fields.image.id, {description: fields.image.description})
        }
        
        // Сохраняем изображения карусели (если есть)
        for (const block of fields.carousel || []) {
            for (const item of block.items || []) {
                if (item.image?.name) {
                    item.image = await this.fileService.save(item.image.originalName, 'personalPage', item.image.name);
                    savedFiles.add(item.image.name);
                }
            }
        }

        if (fields.content) {
            const $ = cheerio.load(fields.content);
            $('img').each(function() {
                const src = $(this).attr('src');
                if (src.includes('upload/tmp')) {
                    const uploadIndex = src.indexOf('/upload');
                    const result = '.' + src.substring(uploadIndex);
                    const baseUrl = process.env.BASE_URL || 'http://localhost:9015';
                    if (existsSync(result)) {
                        copyFileSync(result, `./upload/personalPage/${basename(src)}`);
                        const newSrc = `${baseUrl}/upload/personalPage/${basename(src)}`;
                        $(this).attr('src', newSrc);
                    }
                }
            });
            fields.content = $('body').html();
        }

        // Обновление: чистим старые файлы, если они не используются
        if (fields.id) {
            const oldPage: any = await Mypage.findOneBy({ id: fields.id });

            const oldImages = new Set<string>();
            if (oldPage.content) {
                const $ = cheerio.load(oldPage.content);
                $('img').each(function () {
                    const src = $(this).attr('src');
                    if (src.includes('/upload/personalPage/')) {
                        oldImages.add(src);
                    }
                });
            }

            const newImages = new Set<string>();
            if (fields.content) {
                const $ = cheerio.load(fields.content);
                $('img').each(function () {
                    const src = $(this).attr('src');
                    if (src.includes('/upload/personalPage/')) {
                        newImages.add(src);
                    }
                });
            }

            // Удаление лишних файлов
            oldImages.forEach(oldSrc => {
                if (!newImages.has(oldSrc)) {
                    const uploadIndex = oldSrc.indexOf('/upload');
                    const filePath = '.' + oldSrc.substring(uploadIndex);
                    if (existsSync(filePath)) {
                        rmSync(filePath);
                    }
                }
            });

            // Удаляем фон, если он больше не используется
            const oldBg = JSON.parse(oldPage?.bg || '{}');
            if (oldBg?.name && !savedFiles.has(oldBg.name)) {
                const path = `./upload/${oldBg.name}`;
                if (existsSync(path)) {
                    rmSync(path);
                }
            }

            // // Удаляем старые изображения из карусели
            for (const block of oldPage.carousel || []) {
                for (const item of block.items || []) {
                    const name = item.image?.name;
                    if (name && !savedFiles.has(name)) {
                        const path = `./upload/${name}`;
                        if (existsSync(path)) {
                            rmSync(path);
                        }
                    }
                }
            }

            await Mypage.update(fields.id, fields);
            return await Mypage.findOneBy({ id: fields.id });
        }

        // Новая запись
        if (fields.authorId) {
            fields.author = { id: fields.authorId };
            delete fields.authorId;
        }
        return await Mypage.save(fields);
    }

    async removePage(id: number) {
        const myPage = await Mypage.findOne({
            where: {id}
        })

        for(let carousel of myPage.carousel) {
            for(let item of carousel.items) {
                if(existsSync('./upload/' + item.image.name)) {
                    rmSync('./upload/' + item.image.name, {recursive: true})
                }
            }
        }

        if(myPage.bg) {
            myPage.bg = JSON.parse(myPage.bg);
            if(existsSync(`./upload/${myPage.bg['name']}`)) {
                rmSync(`./upload/${myPage.bg['name']}`, {recursive: true})
            }
        }

        const additional = await Mypage.findBy({parent: id})
        for(let item of additional) await item.remove();
        return await Mypage.delete(id)
    }

    async getUsers() {
        return await User.find({
            select: ['id', 'firstName', 'lastName', 'spiritualName', 'email'],
            where: { active: true }
        });
    }

}
