import { FileService } from "@/api/file/file.service"
import { Advertising } from "@/entity/Advertising"
import { Notification, NotificationType } from '@/entity/Notification'
import { Injectable } from '@nestjs/common'
import { existsSync, rmSync } from "fs"
import slugify from "slugify"

@Injectable()
export class AdvertisingService {
    constructor(private readonly fileService: FileService) {}

    async getAll() {
        return await Advertising.find({
            where: {
                lang: 'ru'
            },
            relations: ['image'],
        })
    }

    async getOne(id: number) {
        return await Advertising.findOne({
            where: {id},
            relations: ['image'],
        })
    }

    async create(dto: any, slug: string | null = null) {
        const ruTitle = dto.find(e => e.lang == 'ru')?.form?.title;
        const ruSlug = slug || slugify(ruTitle, {lower: true});

        for(let item of dto) {
            if(!item.form.title) continue

            if(item.form.image) {
                item.form.image = await this.fileService.save(item.form.image.originalName, 'advertising', item.form.image.name)
            }

            await Advertising.save({
                lang: item.lang,
                slug: ruSlug,
                title: item.form.title,
                description: item.form.description,
                link: item.form.link,
                active: item.form.active,
                advertising: item.form.advertising,
                type: item.form.type,
                freq: item.form.freq,
                date: item.form.date,
                image: item.form.image || null
            })
        }

        await Notification.save({
            type: NotificationType.CONTENT_NEWS_PUBLISHED,
            link: `/ru/news`,
            title: ruTitle
        })

        return { slug: ruSlug };
    }

    async getBySlug(slug: string) {
        return await Advertising.find({
            where: {slug},
            relations: ['image']
        });
    }

    async update(slug: string, dto: any) {
        const advertising = await Advertising.find({
            where: {slug},
            relations: ['image']
        });

        for(let item of advertising) {
            const formData = dto.find(e => e.lang === item.lang);
            if(!formData) continue;

            if(formData.form.image && !formData.form.image?.id) {
                formData.form.image = await this.fileService.save(formData.form.image.originalName, 'advertising', formData.form.image.name, item.image)
            }

            item.title = formData.form.title;
            item.description = formData.form.description;
            item.link = formData.form.link;
            item.active = formData.form.active;
            item.advertising = formData.form.advertising;
            item.type = formData.form.type;
            item.freq = formData.form.freq;
            item.date = formData.form.date;
            if(formData.form.image) {
                item.image = formData.form.image;
            }

            const result = await item.save();
            dto.splice(dto.findIndex(e => e.lang == result.lang), 1);
        }

        const newLangItem = dto.filter(e => e.form.title);
        if(!newLangItem.length) return;
        await this.create(newLangItem, slug);
    }

    async delete(slug: string) {
        const advertising = await Advertising.find({
            where: {slug},
            relations: ['image']
        });

        for(let item of advertising) {
            if(item.image) {
                const imagePath = `./upload/${item.image.name}`;
                if(existsSync(imagePath)) {
                    rmSync(imagePath, {recursive: true});
                }
            }
            await item.remove();
        }
    }
}
