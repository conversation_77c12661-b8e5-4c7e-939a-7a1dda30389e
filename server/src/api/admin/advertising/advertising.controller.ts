import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common'
import { AdvertisingService } from './advertising.service'

@Controller('/admin/advertising')
@Groups('CALENDAR_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class AdvertisingController {
  constructor(private readonly advertisingService: AdvertisingService) {}

  @Get()
  async getAll() {
    return await this.advertisingService.getAll()
  }

  @Delete(':slug')
  async delete(@Param('slug') slug: string) {
    return await this.advertisingService.delete(slug)
  }

  @Post() 
  async create(@Body() dto: any) {
    return await this.advertisingService.create(dto)
  }

  @Get(':slug')
  async getBySlug(@Param('slug') slug: string) {
    return await this.advertisingService.getBySlug(slug)
  }

  @Patch(':slug')
  async update(@Param('slug') slug: string, @Body() dto: any) {
    return await this.advertisingService.update(slug, dto)
  }
}
