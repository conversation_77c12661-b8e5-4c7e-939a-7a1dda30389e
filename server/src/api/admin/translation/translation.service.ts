import { Translation } from "@/entity/Translation"
import { BadRequestException, Injectable } from '@nestjs/common'
import { CreateTranslationDto } from "./translation.dto"

@Injectable()
export class TranslationService {
    async getAll() {
        return await Translation.find()
    }
    async get(id: number) {
        return await Translation.findOneBy({id})
    }
    async create(dto: CreateTranslationDto) {
        return await Translation.save(dto);
    }
    async update(id: number, dto: CreateTranslationDto) {
        return await Translation.update(id, dto);
    }
    async delete(code: string) {
        return await Translation.delete({
            code
        })
    }

    async exportTranslations() {
        const translations = await Translation.find();
        return JSON.stringify(translations, null, 2);
    }

    async importTranslations(file: Express.Multer.File): Promise<any> {
        if (!file) {
            throw new BadRequestException('Файл не был загружен');
        }

        const content = file.buffer.toString('utf8');
        let translations: any[];

        try {
            if (file.mimetype === 'application/json' || file.originalname.endsWith('.json')) {
                translations = JSON.parse(content);
            } else {
                throw new BadRequestException('Поддерживаются только файлы JSON');
            }
        } catch (error) {
            throw new BadRequestException('Неверный формат файла или содержимое');
        }

        const results = [];
        for (const translationData of translations) {
            try {
                const existing = await Translation.findOneBy({ code: translationData.code });
                if (existing) {
                    await Translation.update(existing.id, {
                        code: translationData.code,
                        translations: translationData.translations
                    });
                    results.push({ action: 'updated', code: translationData.code });
                } else {
                    await Translation.save({
                        code: translationData.code,
                        translations: translationData.translations
                    });
                    results.push({ action: 'created', code: translationData.code });
                }
            } catch (error) {
                results.push({ action: 'error', code: translationData.code, error: error.message });
            }
        }

        return { message: 'Импорт завершен', results };
    }
}
