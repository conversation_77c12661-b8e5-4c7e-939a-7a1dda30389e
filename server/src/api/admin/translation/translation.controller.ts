import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Delete, Get, Param, Patch, Post, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { Response } from 'express'
import { CreateTranslationDto } from "./translation.dto"
import { TranslationService } from './translation.service'

@Controller('admin/translation')
@Groups('TRANSLATION_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class TranslationController {
  constructor(private readonly translationService: TranslationService) {}

  @Get()
  async getAll() {
    return await this.translationService.getAll()
  }

  @Get('export')
  async export(@Res() res: Response) {
    const data = await this.translationService.exportTranslations();
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="translations.json"');
    res.send(data);
  }

  @Get(':id')
  async get(@Param('id') id: number) {
    return await this.translationService.get(id)
  }

  @Post('add')
  async create(@Body() dto: CreateTranslationDto) {
    return await this.translationService.create(dto)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() dto: CreateTranslationDto) {
    return await this.translationService.update(id, dto)
  }

  @Delete(':code')
  async delete(@Param('code') code: string) {
    return await this.translationService.delete(code)
  }

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  async import(@UploadedFile() file: Express.Multer.File) {
    return await this.translationService.importTranslations(file);
  }
}
