import { Audio } from "@/entity/Audio";
import { Content } from "@/entity/Content";
import { ForumTopic } from "@/entity/ForumTopic";
import { LibraryTranslation } from "@/entity/LibraryTranslation";
import { PhotoLike } from '@/entity/PhotoLike';
import { PhotoTranslation } from "@/entity/PhotoTranslation";
import { User } from '@/entity/User';
import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';

enum SearchTabTypes {
    ALL = 'Все',
    CONTENT = 'Статьи',
    LIBRARY = 'Книги',
    LECTURES = 'Лекции',
    PHOTO = 'Фото',
    FORUM = 'Форум'
}

@Injectable()
export class SearchService {
    private query: {page: number, tab: string, search: string};
    private user: User = null
    private userData: User = null
    private entities: any = [
        {
            key: 'library',
            tab: SearchTabTypes.LIBRARY,
            entity: LibraryTranslation,
            fields: [
                { name: 'title', exactWeight: 1, partialWeight: 2 },
                { name: 'recomendation', partialWeight: 3 },
                { name: 'annotation', partialWeight: 3 },
                { name: 'content', partialWeight: 4 }
            ],
            relations: [
                { name: 'tags', field: 'name', exactWeight: 5 },
                { name: 'likes', field: 'likes' }
            ],
            lang: 'ru',
            favouritesKey: 'libraryFavourites',
        },
        {
            key: 'content',
            tab: SearchTabTypes.CONTENT,
            entity: Content,
            fields: [
                { name: 'title', exactWeight: 1, partialWeight: 2 },
                { name: 'content', partialWeight: 4 }
            ],
            relations: [
                { name: 'tags', field: 'name', exactWeight: 5 }
            ],
            lang: 'ru',
            favouritesKey: 'favouriteContent',
        },
        {
            key: 'audio',
            tab: SearchTabTypes.LECTURES,
            entity: Audio,
            fields: [
                { name: 'title', exactWeight: 1, partialWeight: 2 },
                { name: 'description', partialWeight: 3 }
            ],
            relations: [
                { name: 'tags', field: 'name', exactWeight: 5 }
            ],
            favouritesKey: 'favourites',
        },
        {
            key: 'forum',
            tab: SearchTabTypes.FORUM,
            entity: ForumTopic,
            fields: [
                { name: 'name', exactWeight: 1, partialWeight: 2 },
                { name: 'description', partialWeight: 3 }
            ],
            favouritesKey: 'topicFavorites',
        },
        {
            key: 'photo',
            tab: SearchTabTypes.PHOTO,
            entity: PhotoTranslation,
            fields: [
                { name: 'title', exactWeight: 1, partialWeight: 2 },
            ],
            relations: [
                { name: 'cover', field: 'description', exactWeight: 1, partialWeight: 2 },
                { name: 'photos', field: 'description', exactWeight: 1, partialWeight: 2 }
            ],
            favouritesKey: 'photoFavourites',
        }
    ];

    async search(query: {tab: string, page: number, search: string}, user: User) {
        this.query = query;
        this.user = user;

        if(this.user) {
            this.userData = await User.findOne({
                where: { id: user.id },
                relations: {
                    libraryFavourites: true,
                    favourites: true,
                    favouriteContent: true,
                    photoFavourites: true,
                    topicFavorites: {
                        topic: true
                    }
                },
                select: {
                    libraryFavourites: { id: true },
                    favourites: { id: true },
                    favouriteContent: { id: true, parent_id: true },
                    photoFavourites: { id: true },
                    topicFavorites: { id: true, topic: true }
                }
            });
        }

        if(query.tab == SearchTabTypes.ALL) {
            const itemsPerPage = 10;
            let allItems = [];

            for (const item of this.entities) {
                const result: any = await this.getResults(item, true);
                allItems = [...allItems, ...result.items];
            }

            allItems.sort((a, b) => a.relevance - b.relevance);

            const totalItems = allItems.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startIndex = (this.query.page - 1) * itemsPerPage;
            const paginatedItems = allItems.slice(startIndex, startIndex + itemsPerPage);

            return {
                items: paginatedItems,
                pagination: {
                    total: totalItems,
                    page: this.query.page,
                    itemsPerPage,
                    totalPages
                }
            };
        }

        const entity = this.entities.find((entity) => entity.tab === query.tab);
        return await this.getResults(entity);
    }

    async getResults(item: any, disablePagination = false) {
        const limit = 10;
        const page = this.query.page || 1;
        const offset = (page - 1) * limit;

        const query = this.query.search.toLowerCase();
        const queryBuilder = item.entity.createQueryBuilder('entity');

        if(['content', 'audio'].includes(item.key)) {
            queryBuilder
              .leftJoinAndSelect('entity.likes', 'likes')
              .leftJoinAndSelect('likes.user', 'likesUser');
        }

        if(item.key == 'forum') {
            queryBuilder
              .leftJoinAndSelect('entity.likes', 'likes')
              .leftJoinAndSelect('likes.user', 'likesUser')
              .leftJoinAndSelect('entity.comments', 'comments')
              .leftJoinAndSelect('comments.user', 'user')
              .leftJoinAndSelect('entity.avatar', 'avatar')
              .leftJoinAndSelect('entity.category', 'category');
        }

        if (item.relations) {
            item.relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`entity.${relation.name}`, relation.name);
            });
        }

        if (item.lang) {
            queryBuilder.where('entity.lang = :lang', { lang: item.lang });
        }

        const whereConditions: string[] = [];
        const params: any = { query: `%${query}%`, exactQuery: query };

        item.fields.forEach(field => {
            if (field.exactWeight) whereConditions.push(`LOWER(entity.${field.name}) = :exactQuery`);
            if (field.partialWeight) whereConditions.push(`LOWER(entity.${field.name}) LIKE :query`);
        });

        if (item.relations) {
            item.relations.forEach(relation => {
                if (relation.exactWeight) whereConditions.push(`LOWER(${relation.name}.${relation.field}) = :exactQuery`);
                if (relation.partialWeight) whereConditions.push(`LOWER(${relation.name}.${relation.field}) LIKE :query`);
            });
        }

        if (whereConditions.length > 0) {
            queryBuilder.andWhere(`(${whereConditions.join(' OR ')})`);
        }
        queryBuilder.setParameters(params);

        const caseParts: string[] = [];
        item.fields.forEach(field => {
            if (field.exactWeight) caseParts.push(`WHEN LOWER(entity.${field.name}) = :exactQuery THEN ${field.exactWeight}`);
            if (field.partialWeight) caseParts.push(`WHEN LOWER(entity.${field.name}) LIKE :query THEN ${field.partialWeight}`);
        });
        if (item.relations) {
            item.relations.forEach(relation => {
                if (relation.exactWeight) caseParts.push(`WHEN LOWER(${relation.name}.${relation.field}) = :exactQuery THEN ${relation.exactWeight}`);
                if (relation.partialWeight) caseParts.push(`WHEN LOWER(${relation.name}.${relation.field}) LIKE :query THEN ${relation.partialWeight}`);
            });
        }

        const relevanceExpression = `CASE ${caseParts.join(' ')} ELSE 99 END`;
        queryBuilder.addSelect(relevanceExpression, "relevance");

        queryBuilder.orderBy("relevance", "ASC");

        let total = 0;
        if (!disablePagination) {
            total = await queryBuilder.getCount();
            queryBuilder.skip(offset).take(limit);
        }

        const { entities, raw: rawResults } = await queryBuilder.getRawAndEntities();

        const items = entities.map((entity, index) => {
            return {
                ...entity,
                relevance: rawResults[index]?.relevance,
            };
        });

        if(!total) {
            total = items.length;
        }

        let photoLikes = [];
        if(item.key == 'photo') {
            const ids = items.flatMap(item => item.photos?.map(photo => photo.id) || []).filter(id => id);

            if (ids.length > 0) {
                photoLikes = await PhotoLike.find({
                    where: {
                        file: { id: In(ids) }
                    },
                    relations: { user: true, file: true }
                });
            }
        }

        return {
            items: items.map((e: any) => {
                if(e.photos) {
                    for(let i in e.photos) {
                        e.photos[i] = {
                            ...e.photos[i],
                            likes: photoLikes.filter((k: any) => k.file.id == e.photos[i].id).length,
                            liked: this.user && photoLikes.some((k: any) => k.file.id == e.photos[i].id && this.user.id == k.user.id),
                            inFavourites: this.userData && this.userData['photoFavourites'] && this.userData['photoFavourites'].some((k: any) => {
                                return k.id == e.photos[i].id
                            }),
                        }
                    }
                }
                return {
                    ...e, type: item.key,
                    likes: e.likes ? e.likes.length : 0,
                    liked: !!(e.likes && this.user && e.likes.some((e: any) => (e.email && e?.id === this.user.id) || e?.user?.id === this.user.id)),
                    inFavourites: item.key == 'forum' ? this.userData?.topicFavorites.map((e: any) => e.topic ? e.topic.id : null).filter((e: any) => e).includes(e.id) : item.favouritesKey && this.userData && this.userData[item.favouritesKey] && this.userData[item.favouritesKey].some((k: any) => k?.parent_id ? e.id === k.parent_id : e.id === k.id),
                }
            }),
            pagination: {
                total,
                page,
                itemsPerPage: limit,
                totalPages: Math.ceil(total / limit)
            }
        }
    }
}