import { FileService } from '@/api/file/file.service'
import { ForumCategory } from "@/entity/ForumCategory"
import { ForumTopic } from "@/entity/ForumTopic"
import { ForumTopicComment } from "@/entity/ForumTopicComment"
import { ForumTopicCommentFavorite } from "@/entity/ForumTopicCommentFavorite"
import { ForumTopicCommentLike } from "@/entity/ForumTopicCommentLike"
import { ForumTopicFavorite } from "@/entity/ForumTopicFavorite"
import { ForumTopicLike } from "@/entity/ForumTopicLike"
import { Notification, NotificationType } from '@/entity/Notification'
import { User } from "@/entity/User"
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { MoreThanOrEqual } from 'typeorm'

@Injectable()
export class ForumService {
  constructor(private readonly fileService: FileService) {}
  async getCategory(id: number, user: any, page: number, limit: number = 5, sort = 'dateDesc') {
    const category = await ForumCategory.findOne({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException('Категория не найдена');
    }

    let topics: ForumTopic[];
    let totalTopics: number;

    const relations = [
      'user',
      'images',
      'avatar',
      'category',
      'comments',
      'comments.user',
      'likes',
      'favorites'
    ];

    let orderOptions = {};
    switch (sort) {
      case 'dateAsc':
        orderOptions = { createdAt: 'ASC' };
        break;
      case 'viewsAsc':
        orderOptions = { views: 'ASC' };
        break;
      case 'viewsDesc':
        orderOptions = { views: 'DESC' };
        break;
      case 'lastReplyAsc':
        orderOptions = { lastReplyAt: 'ASC' };
        break;
      case 'lastReplyDesc':
        orderOptions = { lastReplyAt: 'DESC' };
        break;
      case 'dateDesc':
      default:
        orderOptions = { createdAt: 'DESC' };
        break;
    }

    [topics, totalTopics] = await ForumTopic.findAndCount({
      where: { category: { id: id } },
      relations: relations,
      order: orderOptions,
      skip: (page - 1) * limit,
      take: limit,
    });

    for (let topic of topics) {
      topic['access'] = this.getAccess(user, topic);
    }

    return {
      ...category,
      topics: topics,
      totalTopics: totalTopics,
      currentPage: page,
      totalPages: Math.ceil(totalTopics / limit)
    };
  }

    getAccess(user: any, topic: any) {
        if(!user) return [];
        if(user.id === topic.user.id ||
            ['ADMIN', 'FORUM_ADMIN', 'FORUM_MODERATOR'].some(e => user?.groups?.includes(e))
        ) {
          return ['edit', 'remove']
        }
        return []
    }

    async getCategories(user: any, page = 1) {
        const itemsPerPage = 4;

        const categories = await ForumCategory.find({
            order: {
                order: 'ASC'
            },
            relations: ['topics.comments'],
            where: {
              active: true
            },
            take: 4,
            skip: (page - 1) * itemsPerPage
        });

        const total = await ForumCategory.count({
          where: {active: true}
        });

        const items = categories.filter(category => {
          return !category.unavailableStatuses.some(status => user?.statuses?.includes(status));
        });
        return {
          items,
          pagination: {
            total,
            page,
            itemsPerPage,
            totalPages: Math.ceil(total / itemsPerPage)
          }
        }
    }

    async getTopic(id: number, user: any) {
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['images', 'avatar', 'category', 'user', 'user.avatar', 'comments', 'comments.user', 'comments.user.avatar', 'comments.likes', 'comments.likes.user', 'likes', 'likes.user', 'favorites', 'favorites.user', 'comments.favorites', 'comments.favorites.user', 'comments.reply.user', 'comments.reply.user.avatar'],
            order: {
                comments: {
                    createdAt: 'ASC'
                }
            },
          select: {
              comments: {
                id: true,
                createdAt: true,
                comment: true,
                likes: true,
                favorites: true,
                user: true
              }
          }
        })
        if(user && topic.comments) {
            for(let i in topic.comments) {
                if(topic.comments[i].likes.some(l => l.user.id === user.id)) {
                    topic.comments[i]['isLiked'] = true
                }
                if(topic.comments[i].favorites.some(l => l.user.id === user.id)) {
                    topic.comments[i]['isFavorite'] = true
                }
                if(topic.comments[i].user.id === user.id || ['ADMIN', 'FORUM_MODERATOR'].some(e => user.groups?.includes(e))) {
                    topic.comments[i]['access'] = ['edit', 'remove']
                }
            }
            if(topic.likes.some(l => l.user.id === user.id)) {
                topic['isLiked'] = true
            }
            if(topic.favorites.some(l => l.user.id === user.id)) {
                topic['isFavorite'] = true
            }
        }
        await ForumTopic.update(id, {views: ++topic.views})
        return topic
    }

    async createTopic(body: any, user: any) {
        if(body.id) {
            const topic = await ForumTopic.findOne({
                where: {id: body.id},
                relations: ['images', 'avatar']
            })
            topic.images = body.images;
            if(body.avatar && !body.avatar.id) {
                topic.avatar = await this.fileService.save(body.avatar.originalName, 'forum-avatars', body.avatar.name, topic.avatar);
            } else if(body.avatar && body.avatar.id) {
                topic.avatar = body.avatar;
            }
            await topic.save();
            delete body.images;
            delete body.avatar;
            delete body.id;
            return await ForumTopic.update(topic.id, body)
        }

        if(await ForumTopic.findOneBy({name: body.name, category: body.category})) {
          throw new BadRequestException(`Тема уже существует`);
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const topicsTodayCount = await ForumTopic.count({
          where: {
            user: { id: user.id },
            createdAt: MoreThanOrEqual(today)
          }
        });

        if (topicsTodayCount >= 3) {
          throw new BadRequestException('Вы не можете создавать более 3 тем в день');
        }

        if(body.avatar && !body.avatar.id) {
            body.avatar = await this.fileService.save(body.avatar.originalName, 'forum-avatars', body.avatar.name);
        }

        return await ForumTopic.save({...body, user: user.id})
    }

    async deleteTopic(id: number) {
        return await ForumTopic.delete(id)
    }

    async saveComment(body: any, user: any) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const commentsTodayCount = await ForumTopicComment.count({
          where: {
            user: { id: user.id },
            createdAt: MoreThanOrEqual(today)
          }
        });

        if (commentsTodayCount >= 10) {
          throw new BadRequestException('Вы не можете оставлять более 10 комментариев в день');
        }

        const lastComment = await ForumTopicComment.findOne({
          where: { user: { id: user.id } },
          order: { createdAt: 'DESC' },
        });

        if (lastComment) {
          const timeDifference = (new Date().getTime() - lastComment.createdAt.getTime()) / 1000;
          if (timeDifference < 30) {
            throw new BadRequestException('Вы можете оставлять комментарии только раз в 30 секунд');
          }
        }

        let reply = null

        ForumTopic.update(body.topic, { lastReplyAt: new Date()});

        if(body.id) {
            return await ForumTopicComment.update(body.id, {comment: body.comment})
        }
        // const comment = await ForumTopicComment.findOne({
        //     where: {
        //         reply: {
        //             id: body.reply.id
        //         }
        //     }
        // })

        const forumTopic = await ForumTopic.findOne({where: {id: body.topic}, select: ['user', 'name', 'id'], relations: ['user']});

        if(body.reply) {
            const forumTopicReply = await ForumTopicComment.findOne({where: {id: body.reply.id}, select: ['user', 'topic'], relations: ['user']})
            
            reply = [{id: body.reply.id}];

            if(forumTopicReply.user.id !== user.id) {
              await Notification.save({
                type: NotificationType.FORUM_POST_REPLY,
                title: forumTopic.name,
                link: `/ru/forum/topic/${forumTopic.id}`,
                user: forumTopicReply.user
              })
            }
        }

        await Notification.save({
            type: NotificationType.FORUM_TOPIC_CREATED_ACTIVITY,
            title: forumTopic.name,
            link: `/ru/forum/topic/${forumTopic.id}`,
            user: forumTopic.user
        })

        return await ForumTopicComment.save({...body, user: user.id, reply})
    }

    async likeComment(id: number, user: any) {
        const liked = await ForumTopicCommentLike.findOneBy({topicComment: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicCommentLike.delete(liked.id)
        }
        const topic = await ForumTopicComment.findOne({
            where: {id},
            relations: ['likes', 'user', 'topic']
        });
        const like = await ForumTopicCommentLike.save({
            user,
            topicComment: {id}
        })
        topic.likes = [...topic.likes, like];

        await Notification.save({
            type: NotificationType.FORUM_POST_LIKE,
            title: topic.topic.name,
            link: `/ru/forum/topic/${topic.topic.id}`,
            user: topic.user
        })

        return await topic.save();
    }

    async favoriteComment(id: number, user: any) {
        const liked = await ForumTopicCommentFavorite.findOneBy({topicComment: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicCommentFavorite.delete(liked.id)
        }
        const topic = await ForumTopicComment.findOne({
            where: {id},
            relations: ['favorites', 'user', 'topic']
        });
        const like = await ForumTopicCommentFavorite.save({
            user,
            topicComment: {id}
        })
        topic.favorites = [...topic.favorites, like];

        await Notification.save({
            type: NotificationType.FORUM_POST_FAVORITED,
            title: topic.topic.name,
            link: `/ru/forum/topic/${topic.topic.id}`,
            user: topic.user
        })

        return await topic.save();
    }

    async likeTopic(id: number, user: any) {
        const liked = await ForumTopicLike.findOneBy({topic: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicLike.delete(liked.id)
        }
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['likes']
        });
        const like = await ForumTopicLike.save({
            user,
            topic: {id}
        })
        topic.likes = [...topic.likes, like];
        return await topic.save();
    }

    async favoriteTopic(id: number, user: any) {
        const isFavorite = await ForumTopicFavorite.findOneBy({topic: {id}, user: {id: user.id}})
        if(isFavorite) {
            return await ForumTopicFavorite.delete(isFavorite.id)
        }
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['favorites']
        });
        const like = await ForumTopicFavorite.save({
            user,
            topic: {id}
        })
        topic.favorites = [...topic.favorites, like];
        return await topic.save();
    }

    async deleteComment(id: number) {
        return await ForumTopicComment.delete(id)
    }

    async getFavourites(userId: number) {
        const userEntity: any = await User.findOne({
            where: {id: userId},
            relations: [
                'topicFavorites.topic.category',
                'topicFavorites.topic.avatar',
                'topicFavorites.topic.likes.user',
                'topicCommentFavorites.topicComment',
                'topicCommentFavorites.topicComment.topic.category',
                'topicCommentFavorites.topicComment.topic.avatar',
                'topicFavorites.topic.comments',
                'topicFavorites.topic.comments.user',
                'topicCommentFavorites.topicComment.likes.user'
            ]
        });

        for(let i in userEntity.topicFavorites) {
            userEntity.topicFavorites[i] = {
                ...userEntity.topicFavorites[i],
                topic: {
                    ...userEntity.topicFavorites[i].topic,
                    likes: userEntity.topicFavorites[i].topic.likes.length,
                    liked: userEntity.topicFavorites[i].topic.likes.some((e) => e.user.id === userId)
                }
            }
        }

        for(let i in userEntity.topicCommentFavorites) {
            userEntity.topicCommentFavorites[i] = {
                ...userEntity.topicCommentFavorites[i],
                topicComment: {
                    ...userEntity.topicCommentFavorites[i].topicComment,
                    likes: userEntity.topicCommentFavorites[i].topicComment.likes.length,
                    liked: userEntity.topicCommentFavorites[i].topicComment.likes.some((e) => e.user.id === userId)
                }
            }
        }

        return userEntity;
    }

    async disableComments(body, user: any) {
      return await ForumTopic.update(body.topicId, {disableComments: body.value})
    }
}
