import { FileService } from '@/api/file/file.service';
import { ForumTopic } from "@/entity/ForumTopic";
import { ForumTopicComment } from "@/entity/ForumTopicComment";
import { ForumTopicCommentFavorite } from "@/entity/ForumTopicCommentFavorite";
import { ForumTopicCommentLike } from "@/entity/ForumTopicCommentLike";
import { ForumTopicFavorite } from "@/entity/ForumTopicFavorite";
import { ForumTopicLike } from "@/entity/ForumTopicLike";
import { Module } from '@nestjs/common';
import { TypeOrmModule } from "@nestjs/typeorm";
import { ForumController } from './forum.controller';
import { ForumService } from './forum.service';

@Module({
  imports: [TypeOrmModule.forFeature([ForumTopic, ForumTopicComment, ForumTopicCommentLike, ForumTopicLike, ForumTopicFavorite, ForumTopicCommentFavorite])],
  controllers: [ForumController],
  providers: [ForumService, FileService],
})
export class ForumModule {}
