import { Mypage } from "@/entity/Mypage";
import { Injectable } from '@nestjs/common';

@Injectable()
export class MypageService {
    async getOne(id: number, lang: string) {
        let result = await Mypage.findOne({
            where: lang === 'ru' ? {id} : {parent: id, lang},
            relations: ['image']
        })
        if(!result){
            result = await Mypage.findOne({
                where: {id},
                relations: ['image']
            })
        }
        result.bg = JSON.parse(result.bg)
        return result
    }
}
