import { Auth } from "@/api/user/decorators/auth.decorator"
import { User } from "@/api/user/decorators/user.decorator"
import { OptionalJwtAuthGuard } from "@/api/user/guards/auth.optional.guard"
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards
} from '@nestjs/common'
import { PaymentProviderType } from '../donation/create-payment.dto'
import { IPaymentProvider } from '../donation/donation.service'
import { StripeService } from '../donation/payment-providers/stripe.service'
import { YookassaService } from '../donation/payment-providers/yookassa.service'
import { ContentService } from './content.service'

@Controller('client/content')
export class ContentController {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
      private contentService: ContentService,
      private readonly yookassaService: YookassaService,
      private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
    this.contentService.setProviders(this.providers);
  }

  @Get()
  @UseGuards(OptionalJwtAuthGuard)
  async getAll(
      @Query() query: any,
      @User() user: any
  ) {
    return await this.contentService.getAll(query, user)
  }

  @Get('link/:id')
  async getLink(@Param('id') id: number) {
    return await this.contentService.getLink(id)
  }

  @Get('categories')
  async getCategories() {
    return await this.contentService.getCategories()
  }


  @Get('quote')
  async getQuote(@Query('id') id: number) {
    return await this.contentService.getQuote(id)
  }

  @Get('likes')
  @Auth()
  async getLikes(
      @Req() req,
      @Query('slug') slug: string
  ) {
    return await this.contentService.getLikes(req.user.id, slug);
  }

  @Get('favourites')
  @Auth()
  async getFavourites(@Req() req) {
    return await this.contentService.getFavourites(req.user.id);
  }

  @Get('favourites/ids')
  @UseGuards(OptionalJwtAuthGuard)
  async getFavouritesByIds(
      @Query('ids') ids: number[],
      @Query('page') page: number,
      @User() user: any
  ) {
    return await this.contentService.getFavouritesByIds(ids, page, user);
  }

  @Get('similar')
  async getSimilar(@Query('slug') slug: string) {
    return await this.contentService.getSimilar(slug);
  }

  @Get(':page')
  @UseGuards(OptionalJwtAuthGuard)
  async getOne(
    @Param('page') page: string,
    @Query('lang') lang: string,
    @Query('views') views: boolean,
    @User() user: any
    ) {
    return await this.contentService.getOne(page, lang, views, user)
  }

  @Post('favourite')
  @Auth()
  async favourite(@Body('id') id: number, @Req() req) {
    return await this.contentService.favourite(id, req.user.id)
  }

  @Post('like')
  @Auth()
  async like(@Body('id') id: number, @Req() req) {
    return await this.contentService.like(id, req.user.id)
  }

  @Post('quote')
  @Auth()
  async addQuoteToFavourites(@Body() dto: any, @Req() req: any) {
    return await this.contentService.addQuoteToFavourites(req.user, dto);
  }

  @Post('purchase')
  @Auth()
  async purchase(@Req() req: any, @Body() body: any) {
    return await this.contentService.purchase(req.user, body);
  }

}
